/**
 * Contact Processor for AP Webhook Events
 *
 * Handles contact creation and update events from AutoPatient webhooks by:
 * 1. Checking for calendar property (skip if present)
 * 2. Looking up existing contact in local database
 * 3. Fetching complete contact details from AutoPatient API
 * 4. Comparing timestamps for recent updates
 * 5. Syncing with CliniCore platform
 * 6. Updating local database with results
 *
 * Performance Critical: Must complete within 20 seconds (Cloudflare Workers timeout)
 */

import { dbSchema, getDb } from "@database";
import type {
	APContactWebhookPayload,
	APWebhookPayload,
	GetAPContactType,
	GetCCPatientType,
	PostCCPatientType,
	APGetCustomFieldType,
} from "@type";
import { eq, or } from "drizzle-orm";
import { contactReq, patientReq, ccCustomfieldReq, apCustomfield } from "@/apiClient";
import { logDebug, logError, logInfo, logProcessingStep, logWarn } from "@/utils/logger";
import { getConfig } from "@/utils/configs";
import type { PostCCP<PERSON>ent<PERSON>ustomfield, GetCCCustomField } from "@type";
import cleanData from "@/utils/cleanData";
import { fieldNamesMatch } from "@/processors/cc/fieldMatcher";
import {
	transformApToCcValue,
	areFieldTypesCompatible,
	logTypeConversion,
} from "./typeConverter";


/**
 * Normalize contact field values (email, phone) following v3Integration patterns
 * Converts empty strings and whitespace-only strings to null
 *
 * @param value - Raw field value
 * @returns Normalized value or null
 */
function normalizeContactField(value: string | undefined | null): string | null {
	if (!value || typeof value !== 'string') return null;
	const trimmed = value.trim();
	return trimmed === '' ? null : trimmed;
}

/**
 * Supported field value types for CC custom fields
 */
type CCCustomFieldValue = string | number | string[] | number[];

/**
 * Enhanced field name to value mapping with multiple data type support
 */
type CCCustomFieldMap = Record<string, CCCustomFieldValue>;

/**
 * Properly typed patient update payload that includes custom fields
 * This extends PostCCPatientType to include the custom field data structure
 * that v3Integration uses (bypassing the number[] constraint)
 */
interface CCPatientUpdatePayload {
	customFields: PostCCPatientCustomfield[];
}

/**
 * Validate and normalize field value based on supported types
 *
 * @param value - Raw field value
 * @param fieldName - Field name for error reporting
 * @returns Normalized value or throws error for unsupported types
 */
function validateAndNormalizeFieldValue(value: CCCustomFieldValue, fieldName: string): CCCustomFieldValue {
	if (value === null || value === undefined) {
		throw new Error(`Field "${fieldName}" has null or undefined value`);
	}

	if (typeof value === 'string') {
		const trimmed = value.trim();
		if (trimmed === '') {
			throw new Error(`Field "${fieldName}" has empty string value`);
		}
		return trimmed;
	}

	if (typeof value === 'number') {
		if (!Number.isFinite(value)) {
			throw new Error(`Field "${fieldName}" has invalid number value: ${value}`);
		}
		return value;
	}

	if (Array.isArray(value)) {
		if (value.length === 0) {
			throw new Error(`Field "${fieldName}" has empty array value`);
		}

		// Check if all elements are strings
		if (value.every(item => typeof item === 'string')) {
			const normalizedArray = (value as string[]).map((item, index) => {
				const trimmed = item.trim();
				if (trimmed === '') {
					throw new Error(`Field "${fieldName}" has empty string at index ${index}`);
				}
				return trimmed;
			});
			return normalizedArray;
		}

		// Check if all elements are numbers
		if (value.every(item => typeof item === 'number')) {
			const normalizedArray = (value as number[]).map((item, index) => {
				if (!Number.isFinite(item)) {
					throw new Error(`Field "${fieldName}" has invalid number at index ${index}: ${item}`);
				}
				return item;
			});
			return normalizedArray;
		}

		// Mixed types not supported
		throw new Error(`Field "${fieldName}" has mixed array element types - only string[] or number[] are supported`);
	}

	throw new Error(`Field "${fieldName}" has unsupported value type: ${typeof value}`);
}

/**
 * Standardized field matching using only fieldNamesMatch method
 *
 * Replaces multi-tier matching logic with consistent fieldNamesMatch approach
 * as required for bidirectional custom field type conversion.
 *
 * @param ccCustomFields - Available CC custom fields
 * @param fieldName - Field name to match
 * @param requestId - Request ID for logging
 * @returns Matching CC custom field or undefined
 */
function findMatchingCCCustomField(
	ccCustomFields: GetCCCustomField[],
	fieldName: string,
	requestId: string
): GetCCCustomField | undefined {
	logDebug(requestId, `Searching for CC custom field match using fieldNamesMatch for: "${fieldName}"`);

	// Use only fieldNamesMatch for consistent field matching
	// This matches both field name and label using normalized comparison
	const match = ccCustomFields.find((ccf) =>
		fieldNamesMatch(ccf.name, fieldName) ||
		fieldNamesMatch(ccf.label, fieldName)
	);

	if (match) {
		logDebug(requestId, `Found fieldNamesMatch: "${match.label}" (${match.name})`);
		return match;
	}

	logDebug(requestId, `No CC custom field match found for: "${fieldName}"`);
	return undefined;
}

/**
 * Find matching AP custom field using fieldNamesMatch method
 *
 * Used for type detection and bidirectional conversion support.
 *
 * @param apCustomFields - Available AP custom fields
 * @param fieldName - Field name to match
 * @param requestId - Request ID for logging
 * @returns Matching AP custom field or undefined
 */
function findMatchingAPCustomField(
	apCustomFields: APGetCustomFieldType[],
	fieldName: string,
	requestId: string
): APGetCustomFieldType | undefined {
	logDebug(requestId, `Searching for AP custom field match using fieldNamesMatch for: "${fieldName}"`);

	// Use fieldNamesMatch for consistent field matching
	// Check against field name and fieldKey (without contact. prefix)
	const match = apCustomFields.find((apf) => {
		const fieldKey = apf.fieldKey?.replace("contact.", "") || apf.name;
		return fieldNamesMatch(apf.name, fieldName) || fieldNamesMatch(fieldKey, fieldName);
	});

	if (match) {
		logDebug(requestId, `Found AP fieldNamesMatch: "${match.name}" (ID: ${match.id}, type: ${match.dataType})`);
		return match;
	}

	logDebug(requestId, `No AP custom field match found for: "${fieldName}"`);
	return undefined;
}

/**
 * Create custom field values array handling different data types and allowedValues constraints
 *
 * @param fieldValue - Validated field value
 * @param ccField - Matching CC custom field definition
 * @param fieldName - Field name for logging
 * @param requestId - Request ID for logging
 * @returns Array of custom field value objects
 */
function createCustomFieldValues(
	fieldValue: CCCustomFieldValue,
	ccField: GetCCCustomField,
	fieldName: string,
	requestId: string
): Array<{ id?: number; value?: string }> {
	const values: Array<{ id?: number; value?: string }> = [];

	// Convert field value to array for uniform processing
	const valueArray = Array.isArray(fieldValue) ? fieldValue : [fieldValue];

	for (const singleValue of valueArray) {
		const stringValue = String(singleValue);

		// Handle allowedValues constraint for dropdown/select fields (v3Integration logic)
		if (ccField.allowedValues && ccField.allowedValues.length > 0) {
			const allowedValue = ccField.allowedValues.find((v) => v.value === stringValue);
			if (allowedValue) {
				// Use ID for predefined values
				values.push({ id: allowedValue.id });
				logDebug(requestId, `Using predefined value ID ${allowedValue.id} for field "${fieldName}" value "${stringValue}"`);
			} else {
				logInfo(requestId, `Value "${stringValue}" not found in allowed values for field "${fieldName}", skipping this value`);
				// Skip this value but continue with others
			}
		} else {
			// Free text field - use the string value directly
			values.push({ value: stringValue });
			logDebug(requestId, `Using free text value "${stringValue}" for field "${fieldName}"`);
		}
	}

	return values;
}

/**
 * Update CliniCore custom fields for a patient with bidirectional type conversion
 *
 * Enhanced version with:
 * - AP to CC field type conversion and value transformation
 * - Standardized field matching using only fieldNamesMatch method
 * - Graceful handling of unmatchable fields without sync failures
 * - Backward compatibility for existing phone/email sync functionality
 *
 * @param patientId - CC patient ID
 * @param fieldNameValueMap - Map of field names to values supporting string, number, string[], number[]
 * @param requestId - Request ID for logging
 * @returns Promise<void>
 */
async function updateCCCustomFields(
	patientId: number,
	fieldNameValueMap: CCCustomFieldMap,
	requestId: string
): Promise<void> {
	logDebug(requestId, `Starting CC custom fields update with bidirectional type conversion for patient ${patientId}`);

	if (Object.keys(fieldNameValueMap).length === 0) {
		logDebug(requestId, "No custom field values to sync");
		return;
	}

	try {
		// Step 1: Fetch all available CC custom fields
		logDebug(requestId, "Fetching CC custom field definitions");
		const ccCustomFields = await ccCustomfieldReq.all();

		if (!ccCustomFields || ccCustomFields.length === 0) {
			logInfo(requestId, "No CC custom fields available for matching");
			return;
		}

		// Step 2: Fetch AP custom fields for type detection and conversion
		logDebug(requestId, "Fetching AP custom field definitions for type conversion");
		let apCustomFields: APGetCustomFieldType[] = [];
		try {
			apCustomFields = await apCustomfield.all();
			logDebug(requestId, `Fetched ${apCustomFields.length} AP custom fields for type detection`);
		} catch (error) {
			logError(requestId, "Failed to fetch AP custom fields, proceeding without type conversion", error);
			// Continue without AP fields - will use pass-through conversion
		}

		// Step 3: Process and validate field values with type conversion
		const matchedProperties: PostCCPatientCustomfield[] = [];
		let processedCount = 0;
		let convertedCount = 0;
		let unmatchableCount = 0;

		for (const [fieldName, rawFieldValue] of Object.entries(fieldNameValueMap)) {
			try {
				processedCount++;

				// Validate and normalize the field value
				const fieldValue = validateAndNormalizeFieldValue(rawFieldValue, fieldName);

				// Find matching CC custom field using standardized fieldNamesMatch only
				const ccField = findMatchingCCCustomField(ccCustomFields, fieldName, requestId);

				if (!ccField) {
					unmatchableCount++;
					logDebug(requestId, `No CC custom field found for "${fieldName}" - gracefully skipping`);
					continue;
				}

				logDebug(requestId, `Matched field "${fieldName}" to CC custom field "${ccField.label}" (${ccField.name})`);

				// Find matching AP custom field for type conversion
				const apField = findMatchingAPCustomField(apCustomFields, fieldName, requestId);

				let finalFieldValue = fieldValue;

				// Apply type conversion if AP field is found
				if (apField) {
					// Check type compatibility and apply conversion
					if (areFieldTypesCompatible(apField, ccField, requestId)) {
						// Handle both single values and arrays for multi-value fields
						let originalValue: string | string[];
						if (Array.isArray(fieldValue)) {
							// For arrays, convert each element to string
							originalValue = fieldValue.map(v => v.toString());
						} else {
							// For single values, convert to string
							originalValue = fieldValue.toString();
						}

						const convertedValue = transformApToCcValue(originalValue, apField, ccField, requestId);

						// Update the field value with converted value
						// CC custom fields expect string values, even for multi-value (comma-separated)
						finalFieldValue = convertedValue;

						const originalValueStr = Array.isArray(originalValue) ? originalValue.join(', ') : originalValue;
						if (originalValueStr !== convertedValue) {
							convertedCount++;
							logTypeConversion(apField, ccField, originalValueStr, convertedValue, requestId);
						}
					} else {
						logDebug(requestId, `Type conversion skipped for "${fieldName}" due to incompatible types`);
					}
				} else {
					logDebug(requestId, `No AP field found for "${fieldName}", using pass-through conversion`);
				}

				// Handle different value types and create appropriate custom field values
				const customFieldValues = createCustomFieldValues(finalFieldValue, ccField, fieldName, requestId);

				if (customFieldValues.length === 0) {
					logInfo(requestId, `No valid values created for field "${fieldName}", skipping`);
					continue;
				}

				// Create custom field value object (v3Integration structure)
				const customFieldValue: PostCCPatientCustomfield = {
					field: ccField,
					values: customFieldValues,
					patient: null,
				};

				matchedProperties.push(customFieldValue);

			} catch (error) {
				logError(requestId, `Error processing field "${fieldName}": ${error}`);
				// Continue processing other fields
				continue;
			}
		}

		// Log processing statistics
		logInfo(
			requestId,
			`Field processing completed: ${processedCount} total, ${matchedProperties.length} matched, ${convertedCount} converted, ${unmatchableCount} unmatchable`
		);

		if (matchedProperties.length === 0) {
			logInfo(requestId, "No matching CC custom fields found - all fields were unmatchable");
			return;
		}

		// Step 4: Clean and send update request to CC API (v3Integration pattern)
		const payload = cleanData(matchedProperties);
		logInfo(requestId, `Updating ${matchedProperties.length} custom fields for CC patient ${patientId}`);

		// Create properly typed payload that bypasses the PostCCPatientType constraint
		// This follows the v3Integration pattern where custom fields are sent as objects, not IDs
		const updatePayload: CCPatientUpdatePayload = {
			customFields: payload as PostCCPatientCustomfield[]
		};

		await patientReq.update(patientId, updatePayload as any);

		logInfo(
			requestId,
			`Successfully updated CC custom fields for patient ${patientId} with bidirectional type conversion`
		);

	} catch (error) {
		logError(requestId, "CC custom fields update error", error);
		throw new Error(`Failed to update CC custom fields: ${error}`);
	}
}



/**
 * Process contact creation and update webhooks from AutoPatient
 *
 * Implements the 4-step logic:
 * 1. Calendar property check (already done in handler)
 * 2. Local database lookup
 * 3. Timestamp comparison if contact found
 * 4. CliniCore synchronization
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param payload - Validated AP contact webhook payload
 * @returns Promise<void> - Completes processing or throws error
 */
export async function contactSyncProcessor(
	requestId: string,
	payload: APContactWebhookPayload,
): Promise<void> {
	const apContactId: string = payload.contact_id as string;
	const email = typeof payload.email === 'string' ? payload.email.trim() || null : null;
	const phone = typeof payload.phone === 'string' ? payload.phone.trim() || null : null;

	// Validate that either email or phone is present
	if (!email && !phone) {
		const errorMessage = "Processing stopped due to missing required contact data (email or phone)";
		logError(requestId, errorMessage);
		throw new Error(errorMessage);
	}

	logInfo(
		requestId,
		`Processing contact sync for AP ID: ${apContactId}, Email: ${email}, Phone: ${phone}`,
	);

	// Step 2: Local Database Lookup
	const db = getDb();
	let existingPatient: typeof dbSchema.patient.$inferSelect | undefined;

	try {
		// Primary lookup by apId
		const apIdResults = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.apId, apContactId as string))
			.limit(1);

		if (apIdResults.length > 0) {
			existingPatient = apIdResults[0];
			logDebug(requestId, `Found existing patient by AP ID: ${existingPatient.id}`);
		} else {
			// Secondary lookup by email and phone
			if (email || phone) {
				const conditions = [];
				if (email) conditions.push(eq(dbSchema.patient.email, email));
				if (phone) conditions.push(eq(dbSchema.patient.phone, phone));

				const emailPhoneResults = await db
					.select()
					.from(dbSchema.patient)
					.where(or(...conditions))
					.limit(1);

				if (emailPhoneResults.length > 0) {
					existingPatient = emailPhoneResults[0];
					logDebug(requestId, `Found existing patient by email/phone: ${existingPatient.id}`);
				}
			}
		}
	} catch (error) {
		logError(requestId, "Database lookup error", error);
		throw new Error(`Failed to lookup existing patient: ${error}`);
	}

	// Step 3A: If Contact Found in Local Database
	if (existingPatient) {
		logInfo(requestId, `Existing patient found: ${existingPatient.id}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId as string);
			logDebug(requestId, `Fetched AP contact data for: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Compare webhook date_updated with local database apUpdatedAt timestamp
		// Use date_updated from webhook payload if available, otherwise use date_created
		const webhookUpdatedDate = typeof apContactData.dateUpdated === 'string'
			? new Date(apContactData.dateUpdated)
			: new Date(apContactData.dateAdded as string);
		const localUpdatedDate = existingPatient.apUpdatedAt;

		if (localUpdatedDate) {
			const timeDiffMs = Math.abs(webhookUpdatedDate.getTime() - localUpdatedDate.getTime());
			const timeDiffMinutes = timeDiffMs / (1000 * 60);
			const syncBufferMinutes = (getConfig('syncBufferTimeSec') as number) / 60;

			// If timestamps are within the configured sync buffer time: Skip sync
			if (timeDiffMinutes <= syncBufferMinutes) {
				logInfo(
					requestId,
					`Recent update detected (${timeDiffMinutes.toFixed(2)} minutes, buffer: ${syncBufferMinutes} minutes) - skipping sync`,
				);
				return;
			}

			logInfo(
				requestId,
				`Timestamp difference: ${timeDiffMinutes.toFixed(2)} minutes (buffer: ${syncBufferMinutes} minutes) - proceeding with sync`,
			);
		} else {
			logInfo(requestId, "No local timestamp found - proceeding with sync");
		}

		// Continue to CliniCore sync with existing CC ID information
		await syncWithCliniCore(requestId, existingPatient.id, apContactData, true, existingPatient.ccId);
	} else {
		// Step 3B: If Contact NOT Found in Local Database
		logInfo(requestId, `New contact detected: ${apContactId}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId as string);
			logDebug(requestId, `Fetched AP contact data for new contact: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Add to local database
		let localPatientId: string;
		try {
			const now = new Date();
			const newPatientResults = await db
				.insert(dbSchema.patient)
				.values({
					apId: apContactId as string,
					email: email,
					phone: phone,
					apData: apContactData,
					apUpdatedAt: now,
				})
				.returning({ id: dbSchema.patient.id });

			localPatientId = newPatientResults[0].id;
			logInfo(requestId, `Created new patient record: ${localPatientId}`);
		} catch (error) {
			logError(requestId, "Database insert error", error);
			throw new Error(`Failed to create local patient record: ${error}`);
		}

		// Proceed to CliniCore synchronization (no existing CC ID for new patients)
		await syncWithCliniCore(requestId, localPatientId, apContactData, false, null);
	}

	logProcessingStep(
		requestId,
		`Contact sync completed for AP ID: ${apContactId}`,
	);
}

/**
 * Sync patient data with CliniCore platform
 *
 * Step 4: CliniCore Synchronization
 * - Use existing CC ID if available, otherwise search CliniCore for existing patient
 * - If patient found: Update existing patient data
 * - If patient NOT found: Create new patient record
 *
 * @param requestId - Request ID for logging
 * @param localPatientId - Local database patient ID
 * @param apContactData - AP contact data
 * @param isUpdate - Whether this is an update (true) or new contact (false)
 * @param existingCcId - Existing CC patient ID if known (prevents duplicate creation)
 * @returns Promise<void>
 */
async function syncWithCliniCore(
	requestId: string,
	localPatientId: string,
	apContactData: GetAPContactType,
	_isUpdate: boolean, // Prefixed with underscore to indicate intentionally unused
	existingCcId?: number | null,
): Promise<void> {
	const db = getDb();
	const now = new Date();

	// Use AP contact data as authoritative source for email/phone (more complete than webhook payload)
	const email = normalizeContactField(apContactData.email);
	const phone = normalizeContactField(apContactData.phone);

	logInfo(requestId, `Starting CliniCore sync for local patient: ${localPatientId}`);

	// Search CliniCore for existing patient - use existing CC ID if available
	let existingCCPatient: GetCCPatientType | null = null;
	try {
		if (existingCcId) {
			// Use existing CC ID to fetch patient directly
			logDebug(requestId, `Using existing CC ID: ${existingCcId}`);
			try {
				existingCCPatient = await patientReq.get(existingCcId);
				logDebug(requestId, `Found existing CC patient by ID: ${existingCCPatient.id}`);
			} catch (error) {
				logWarn(requestId, `Failed to fetch CC patient by existing ID ${existingCcId}, falling back to search`, error);
				// Fall through to search by email/phone
			}
		}

		// If no existing CC ID or fetch failed, search by email/phone
		if (!existingCCPatient) {
			if (email) {
				existingCCPatient = await patientReq.search(email);
				if (existingCCPatient) {
					logDebug(requestId, `Found existing CC patient by email: ${existingCCPatient.id}`);
				}
			}

			if (!existingCCPatient && phone) {
				existingCCPatient = await patientReq.search(phone);
				if (existingCCPatient) {
					logDebug(requestId, `Found existing CC patient by phone: ${existingCCPatient.id}`);
				}
			}
		}
	} catch (error) {
		logError(requestId, "CliniCore search error", error);
		// Don't throw here - we can still create a new patient
		logInfo(requestId, "Proceeding with patient creation due to search error");
	}

	// Convert AP contact data to CC patient format following v3Integration patterns
	const ccPatientData: PostCCPatientType = {
		firstName: normalizeContactField(apContactData.firstName) || undefined,
		lastName: normalizeContactField(apContactData.lastName) || undefined,
		email: email || undefined,
		phoneMobile: phone || undefined, // Map phone to phoneMobile as per v3Integration pattern
		dob: normalizeContactField(apContactData.dateOfBirth) || undefined,
		gender: normalizeContactField(apContactData.gender) || undefined,
		ssn: normalizeContactField(apContactData.ssn) || undefined,
		// Add address if available (improvement over v3Integration)
		...(apContactData.address1 && {
			addresses: [{
				street: normalizeContactField(apContactData.address1) || undefined,
				city: normalizeContactField(apContactData.city) || undefined,
				state: normalizeContactField(apContactData.state) || undefined,
				zipCode: normalizeContactField(apContactData.postalCode) || undefined,
			}],
		}),
	};

	let finalCCPatient: GetCCPatientType;
	try {
		if (existingCCPatient) {
			// Update existing patient data
			logInfo(requestId, `Updating existing CC patient: ${existingCCPatient.id}`);
			finalCCPatient = await patientReq.update(existingCCPatient.id, ccPatientData);
		} else {
			// Create new patient record
			logInfo(requestId, "Creating new CC patient");
			finalCCPatient = await patientReq.create(ccPatientData);
		}

		logInfo(requestId, `CC sync completed. CC ID: ${finalCCPatient.id}`);

		// CRITICAL: Add phone and email as custom fields following v3Integration pattern
		// v3Integration sends phone/email as BOTH standard fields AND custom fields
		try {
			const customFieldMap: CCCustomFieldMap = {};

			// Add phone and email as custom fields (exact v3Integration pattern)
			if (email) {
				customFieldMap['email'] = email;
			}
			if (phone) {
				customFieldMap['phoneMobile'] = phone;
				customFieldMap['phone-mobile'] = phone;
				customFieldMap['phone'] = phone;
			}

			if (Object.keys(customFieldMap).length > 0) {
				logInfo(requestId, `Syncing ${Object.keys(customFieldMap).length} standard fields as custom fields`);
				await updateCCCustomFields(finalCCPatient.id, customFieldMap, requestId);
			}
		} catch (error) {
			logError(requestId, "Custom fields sync error", error);
			// Don't fail the entire sync if custom fields fail
			logInfo(requestId, "Proceeding with patient sync - custom fields sync failed but patient data was saved");
		}
	} catch (error) {
		logError(requestId, "CliniCore sync error", error);
		throw new Error(`Failed to sync with CliniCore: ${error}`);
	}

	// Update local database with CC sync results
	try {
		await db
			.update(dbSchema.patient)
			.set({
				ccId: finalCCPatient.id,
				ccData: finalCCPatient,
				ccUpdatedAt: now,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, localPatientId));

		logDebug(requestId, `Updated local patient record with CC data: ${localPatientId}`);
	} catch (error) {
		logError(requestId, "Database update error", error);
		throw new Error(`Failed to update local patient record: ${error}`);
	}
}

/**
 * Validate AP contact webhook payload
 *
 * @param payload - Raw webhook payload
 * @returns Validated payload or throws error
 */
export function validateContactWebhookPayload(
	payload: APWebhookPayload,
): APContactWebhookPayload {
	if (!payload || typeof payload !== "object") {
		throw new Error("Invalid payload: must be an object");
	}

	// Ensure calendar property is not present for contact webhooks
	if (payload.calendar) {
		throw new Error("Calendar property found - this should be handled as appointment webhook");
	}

	if (!payload.contact_id) {
		throw new Error("Missing required field: contact_id");
	}

	if (!payload.date_created) {
		throw new Error("Missing required field: date_created");
	}

	if (!payload.location) {
		throw new Error("Missing required field: location");
	}

	// Return as contact webhook payload (calendar property excluded by type)
	return payload as APContactWebhookPayload;
}
